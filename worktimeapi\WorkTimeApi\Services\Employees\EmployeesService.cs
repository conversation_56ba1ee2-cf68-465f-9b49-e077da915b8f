using AutoMapper;
using Gateway.Common.Results;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Addresses;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.DTOs.Roles;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Requests.Roles;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Addresses;
using WorkTimeApi.Database.Repositories.Interfaces.BankAccounts;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Database.Repositories.Interfaces.Payrolls;
using WorkTimeApi.Database.Repositories.Interfaces.Users;
using WorkTimeApi.Models.Mediator;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Permissions;
using WorkTimeApi.Services.Interfaces.PropertyTracking;
using WorkTimeApi.Validators.Interfaces;

namespace WorkTimeApi.Services.Employees
{
    public class EmployeesService(IEmployeesRepository employeesRepository,
        IEmployeeValidator employeeValidator,
        IPayrollsRepository payrollsRepository,
        IUserRepository userRepository,
        IPermissionsService permissionsService,
        IAddressesRepository addressesRepository,
        IBankAccountsRepository bankAccountsRepository,
        IMapper mapper,
        IMediator mediator,
        IPropertyChangeDetectionService propertyChangeDetectionService) : IEmployeesService
    {
        public async Task<Result<EmployeeDTO>> AddEmployeeAsync(EmployeeDTO employeeDTO)
        {
            var employee = mapper.Map<Employee>(employeeDTO);

            var validationResult = await employeeValidator.ValidateEmployeeAsync(employee);
            if (validationResult.HasValidationErrors)
                return validationResult;
            var addedEmployee = await employeesRepository.AddEmployeeAsync(employee);
            await mediator.Publish(new AddDefaultNotificationSettings(addedEmployee.Id, new Guid(DefaultWorktimeRole.Employee)));

            return mapper.Map<EmployeeDTO>(addedEmployee);
        }

        public async Task<EmployeeDTO> AddEmployeeToCompanyAsync(EmployeeDTO employeeDTO, Guid companyId)
        {
            var employee = mapper.Map<Employee>(employeeDTO);

            var addedEmployee = await employeesRepository.AddEmployeeToCompanyAsync(employee, companyId);

            return mapper.Map<EmployeeDTO>(addedEmployee);
        }

        public async Task<EmployeeDTO> GetEmployeeAsync(Guid id)
        {
            var employee = await employeesRepository.GetEmployeeAsync(id);

            return mapper.Map<EmployeeDTO>(employee);
        }

        public async Task<EmployeeDTO> GetUserEmployeeAsync(Guid userId, Guid companyId)
        {
            var employee = await employeesRepository.GetUserEmployeeAsync(userId, companyId);

            return mapper.Map<EmployeeDTO>(employee);
        }

        public async Task<PersonalInformationDTO> GetUserEmployeePersonalDataAsync(Guid userId, Guid companyId)
        {
            var employee = await employeesRepository.GetUserEmployeeAsync(userId, companyId);
            var user = await userRepository.GetByIdAsync(userId);

            var employeeAddress = await employeesRepository.GetEmployeeAddressesAsync(employee.Id);
            var iban = await employeesRepository.GetEmployeeIbanAsync(employee.Id, BankAccountPurpose.Salary);
            var personalInformationDTO = await GetPersonalInformationDTO(employeeAddress, null, employee, iban?.Iban, user?.Code, user?.Email);

            return personalInformationDTO;
        }

        public async Task<IEnumerable<EmployeeDTO>> LoadEmployeesAsync(LoadEmployeesRequest loadEmployeeRequest)
        {
            var employeesDTO = mapper.Map<IEnumerable<EmployeeDTO>>(
                 await employeesRepository.GetEmployeesAsync(loadEmployeeRequest.CompanyId));

            return employeesDTO;
        }

        public async Task<PersonalInformationDTO> LoadEmployeeAsync(LoadEmployeeRequest loadEmployeeRequest)
        {
            var payroll = await payrollsRepository.GetPayrollAsync(loadEmployeeRequest.PayrollId ?? Guid.Empty);

            var employeeAddress = await employeesRepository.GetEmployeeAddressesAsync(loadEmployeeRequest.Id);
            var user = await userRepository.GetEmployeeUserAsync(loadEmployeeRequest.Id);
            var employee = await employeesRepository.GetFullEmployeeAsync(loadEmployeeRequest.Id);
            var iban = await employeesRepository.GetEmployeeIbanAsync(loadEmployeeRequest.Id, BankAccountPurpose.Salary);

            var personalInformationDTO = await GetPersonalInformationDTO(employeeAddress, payroll, employee, iban?.Iban, user?.Code, user?.Email);

            return personalInformationDTO;
        }

        private async Task<PersonalInformationDTO> GetPersonalInformationDTO(List<Address> employeeAddresses, Payroll? payroll, Employee? employee, string? iban, int? code, string? email)
        {
            var result = new PersonalInformationDTO
            {
                Addresses = mapper.Map<List<AddressDTO>>(employeeAddresses),
                Employee = employee != null ? mapper.Map<EmployeeDTO>(employee) : null,
                PayrollPersonalData = payroll != null
                ? new PayrollPersonalDataDTO { ContractNumber = payroll.ContractNumber, ContractType = payroll.ContractType.Value, Id = payroll.Id }
                : new PayrollPersonalDataDTO(),
                Iban = iban,
                Code = code.ToString(),
                Email = email
            };

            var propertyEdits = await GetEmployeePropertyEditsAsync(employee.Id);

            if (propertyEdits.Count != 0)
            {
                result.EmployeePropertyEdits = mapper.Map<List<EmployeePropertyEditDTO>>(propertyEdits);
            }

            return result;
        }

        public async Task<EmployeeDTO> EditEmployeeAsync(EditEmployeeRequest editEmployeeRequest, Guid editorEmployeeId, bool isEditorAdmin)
        {
            var existingEmployee = (await employeesRepository.FindAsync(e => e.Id == editEmployeeRequest.EmployeeId,
                e => e.Include(ee => ee.EmployeeAddresses).ThenInclude(eee => eee.Address)
                .Include(e => e.EmployeeBankAccounts).ThenInclude(ee => ee.BankAccount)))
                .FirstOrDefault();

            if (existingEmployee is null)
                return null;

            // Create a deep copy of the original employee for comparison
            var originalEmployee = await CreateEmployeeCopyAsync(existingEmployee);
            var sessionId = Guid.NewGuid();
            var editSource = isEditorAdmin ? EditSource.AdminEdit : EditSource.UserEdit;

            if (editEmployeeRequest.PersonalData is not null)
            {
                existingEmployee.FirstName = editEmployeeRequest.PersonalData.FirstName;
                existingEmployee.SecondName = editEmployeeRequest.PersonalData.SecondName;
                existingEmployee.LastName = editEmployeeRequest.PersonalData.LastName;
                existingEmployee.BirthDate = editEmployeeRequest.PersonalData.BirthDate;
                existingEmployee.BirthPlace = editEmployeeRequest.PersonalData.BirthPlace;
                existingEmployee.EGN = editEmployeeRequest.PersonalData.EGN;
                existingEmployee.Email = editEmployeeRequest.PersonalData.Email;

                var bankAccount = existingEmployee.EmployeeBankAccounts.FirstOrDefault(b => b.BankAccount.Purpose == BankAccountPurpose.Salary);
                if (bankAccount != null && bankAccount.BankAccount != null)
                {
                    bankAccount.BankAccount.Iban = editEmployeeRequest.PersonalData.IBAN;
                }

                if (editEmployeeRequest.IdentityCardData is not null)
                {
                    existingEmployee.IDIssueDate = editEmployeeRequest.IdentityCardData.IssuedOn;
                    existingEmployee.IDIssuedFrom = editEmployeeRequest.IdentityCardData.IssuedBy;
                    existingEmployee.IDNumber = editEmployeeRequest.IdentityCardData.IdNumber;
                    existingEmployee.Citizenship = editEmployeeRequest.IdentityCardData.Citizenship;
                    existingEmployee.Gender = editEmployeeRequest.IdentityCardData.Gender;

                    if (editEmployeeRequest.IdentityCardData.IdAddress is not null)
                    {
                        var address = await UpdateAddressDataAsync(editEmployeeRequest.IdentityCardData.IdAddress, existingEmployee.Id);
                        if (editEmployeeRequest.IdentityCardData.IdAddress.Id is not null)
                            existingEmployee.EmployeeAddresses.First(ea => ea.AddressId == address.Id).Address = address;
                    }
                }
            }

            if (editEmployeeRequest.AddressForCorrespondence is not null)
            {
                var address = await UpdateAddressDataAsync(editEmployeeRequest.AddressForCorrespondence, existingEmployee.Id);
                if (editEmployeeRequest.AddressForCorrespondence.Id is not null)
                    existingEmployee.EmployeeAddresses.First(ea => ea.AddressId == address.Id).Address = address;
            }

            if (editEmployeeRequest.AddressForRemoteWork is not null)
            {
                var address = await UpdateAddressDataAsync(editEmployeeRequest.AddressForRemoteWork, existingEmployee.Id);
                if (editEmployeeRequest.AddressForRemoteWork.Id is not null)
                    existingEmployee.EmployeeAddresses.First(ea => ea.AddressId == address.Id).Address = address;
            }

            if (editEmployeeRequest.AddressForAbroad is not null)
            {
                var address = await UpdateAddressDataAsync(editEmployeeRequest.AddressForAbroad, existingEmployee.Id);
                if (editEmployeeRequest.AddressForAbroad.Id is not null)
                    existingEmployee.EmployeeAddresses.First(ea => ea.AddressId == address.Id).Address = address;
            }

            if (editEmployeeRequest.OtherAddresses is not null && editEmployeeRequest.OtherAddresses.Any())
            {
                foreach (var address in editEmployeeRequest.OtherAddresses)
                    await UpdateAddressDataAsync(address, existingEmployee.Id);
            }

            await employeesRepository.UpdateAsync(existingEmployee);

            var propertyChanges = propertyChangeDetectionService.DetectEmployeeWithSubObjectChanges(
                originalEmployee,
                existingEmployee,
                editorEmployeeId,
                editSource);

            if (propertyChanges.Any())
            {
                await employeesRepository.UpsertEmployeePropertyEditsAsync(originalEmployee.Id, propertyChanges);
            }

            return mapper.Map<EmployeeDTO>(existingEmployee);
        }

        public Task ImportEmployeeCompaniesAsync(IEnumerable<Guid> employeeIds, Guid companyId)
        {
            return employeesRepository.ImportEmployeeCompaniesAsync(employeeIds, companyId);
        }

        public async Task<List<EmployeeDTO>> GetEmployeesAsync(GetEmployeesByCompanyIdAndPermissionRequest request)
        {
            var employees = await employeesRepository.GetEmployeeAsync(request.CompanyId, request.Permission);

            return mapper.Map<List<EmployeeDTO>>(employees);
        }

        public async Task<Result<EmployeeDTO>> AddEmployeeToCompanyAsync(Guid companyId, EmployeeCompanyStatus status, Guid userId)
        {
            var employee = await employeesRepository.AddEmployeeToCompanyAsync(companyId, status, userId);

            return mapper.Map<EmployeeDTO>(employee);
        }

        public async Task<UserEmployeePermissionsDTO> GetUserEmployeePermissionsAsync(Guid userId, Guid companyId)
        {
            var employee = await employeesRepository.GetUserEmployeePermissionAsync(userId, companyId);
            if (employee == null)
                return new UserEmployeePermissionsDTO { UserId = userId };

            var permissions = await permissionsService.GetRolesPermissionsAsync(mapper.Map<List<RoleDTO>>(employee.EmployeeRoles.Select(er => er.Role)));

            var payrollsDTO = (employee.Payrolls ?? Enumerable.Empty<Payroll>())
            .Select(payroll =>
            {
                var payrollDTO = mapper.Map<PayrollDTO>(payroll);
                payrollDTO.StructureLevelId = payroll.AnnexPayrolls?.LastOrDefault()?.StructureLevelId ?? payrollDTO.StructureLevelId;
                return payrollDTO;
            })
            .ToList();

            return new UserEmployeePermissionsDTO
            {
                EmployeeId = employee.Id,
                UserId = userId,
                Name = string.Join(" ", employee.FirstName, employee.SecondName, employee.LastName),
                Payrolls = payrollsDTO,
                Permissions = permissions.Select(p => p.Name).ToList()
            };
        }

        public async Task<EmployeeDTO> DoesEmployeeExistInCompanyAsync(string email, string egn, Guid companyId)
        {
            var employee = await employeesRepository.DoesEmployeeExistInCompanyAsync(email, egn, companyId);

            return mapper.Map<EmployeeDTO>(employee);
        }

        public async Task DeleteEmployeePropertyEditsAsync(Guid employeeId)
        {
            await employeesRepository.DeleteEmployeePropertyEditsAsync(employeeId);
        }

        public async Task DeleteEmployeePropertyEditAsync(Guid propertyEditId)
        {
            await employeesRepository.DeleteEmployeePropertyEditAsync(propertyEditId);
        }

        public async Task ApproveEmployeePropertyEditAsync(ApproveEmployeePropertyEditRequest request)
        {
            if (request.IsApprovingAllForEmployee && request.EmployeeId.HasValue)
            {
                await employeesRepository.UpdateAllEmployeePropertyEditStatusAsync(request.EmployeeId.Value, EditStatus.Approved);
            }
            else
            {
                await employeesRepository.UpdateEmployeePropertyEditStatusAsync(request.PropertyEditId!.Value, EditStatus.Approved);
            }
        }

        public async Task DeclineEmployeePropertyEditAsync(DeclineEmployeePropertyEditRequest request)
        {
            if (request.IsDecliningAllForEmployee && request.EmployeeId.HasValue)
            {
                var employeeEdits = await employeesRepository.GetPendingEmployeePropertyEditsAsync(request.EmployeeId.Value);

                foreach (var edit in employeeEdits)
                {
                    await RevertEmployeePropertyEditAsync(edit);
                    await employeesRepository.UpdateEmployeePropertyEditStatusAsync(edit.Id, EditStatus.Declined);
                }
            }
            else
            {
                var propertyEdit = await employeesRepository.GetEmployeePropertyEditByIdAsync(request.PropertyEditId!.Value);
                if (propertyEdit != null)
                {
                    await RevertEmployeePropertyEditAsync(propertyEdit);
                    await employeesRepository.UpdateEmployeePropertyEditStatusAsync(request.PropertyEditId!.Value, EditStatus.Declined);
                }
            }
        }

        public async Task<Address> UpdateAddressDataAsync(EmployeeAddressDTO addressDTO, Guid employeeId)
        {
            var address = mapper.Map<Address>(addressDTO);

            if (!addressDTO.Id.HasValue)
            {
                address.EmployeeAddresses.Add(new EmployeeAddress { Address = address, EmployeeId = employeeId, Employee = null });
                return await addressesRepository.AddAsync(address);
            }

            return await addressesRepository.UpdateAddressAsync(address);
        }

        public async Task<EmployeeDTO> GetPayrollEmployeeAsync(Guid payrollId)
        {
            var payroll = await payrollsRepository.GetPayrollAsync(payrollId);

            return mapper.Map<EmployeeDTO>(payroll.Employee);
        }

        private async Task<Employee> CreateEmployeeCopyAsync(Employee employee)
        {
            var settings = new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };

            var json = JsonConvert.SerializeObject(employee, settings);
            var copy = JsonConvert.DeserializeObject<Employee>(json);

            return copy ?? new Employee();
        }

        public async Task<List<EmployeePropertyEditDTO>> GetEmployeePropertyEditsAsync(Guid employeeId)
        {
            var propertyEdits = await employeesRepository.GetEmployeePropertyEditsAsync(employeeId);

            return mapper.Map<List<EmployeePropertyEditDTO>>(propertyEdits);
        }

        private async Task RevertEmployeePropertyEditAsync(EmployeePropertyEdit propertyEdit)
        {
            try
            {
                if (propertyEdit.ObjectName == "Employee")
                {
                    var employee = await employeesRepository.GetByIdAsync(propertyEdit.ObjectId);
                    if (employee != null)
                    {
                        var oldValue = DeserializeValue(propertyEdit.OldValue, employee.GetType().GetProperty(propertyEdit.PropertyName)?.PropertyType);
                        employee[propertyEdit.PropertyName] = oldValue;
                        await employeesRepository.UpdateAsync(employee);
                    }
                }
                else if (propertyEdit.ObjectName == "Address")
                {
                    var employee = await employeesRepository.GetFullEmployeeAsync(propertyEdit.EmployeeId);
                    if (employee != null)
                    {
                        var address = employee.EmployeeAddresses?.FirstOrDefault(ea => ea.AddressId == propertyEdit.ObjectId)?.Address;
                        if (address != null)
                        {
                            var oldValue = DeserializeValue(propertyEdit.OldValue, address.GetType().GetProperty(propertyEdit.PropertyName)?.PropertyType);
                            address[propertyEdit.PropertyName] = oldValue;
                            await addressesRepository.UpdateAsync(address);
                        }
                    }
                }
                else if (propertyEdit.ObjectName == "BankAccount")
                {
                    var employee = await employeesRepository.GetFullEmployeeAsync(propertyEdit.EmployeeId);
                    if (employee != null)
                    {
                        var bankAccount = employee.EmployeeBankAccounts?.FirstOrDefault(eba => eba.BankAccountId == propertyEdit.ObjectId)?.BankAccount;
                        if (bankAccount != null)
                        {
                            var oldValue = DeserializeValue(propertyEdit.OldValue, bankAccount.GetType().GetProperty(propertyEdit.PropertyName)?.PropertyType);
                            bankAccount[propertyEdit.PropertyName] = oldValue;
                            await bankAccountsRepository.UpdateAsync(bankAccount);
                        }
                    }
                }
            }
            catch
            {
            }
        }

        private object? DeserializeValue(string? serializedValue, Type? targetType)
        {
            if (string.IsNullOrEmpty(serializedValue) || targetType == null)
                return null;

            try
            {
                if (targetType == typeof(string))
                    return serializedValue;

                if (targetType.IsPrimitive || targetType == typeof(DateTime) || targetType == typeof(DateTime?) || targetType == typeof(Guid) || targetType == typeof(decimal))
                {
                    return Convert.ChangeType(serializedValue, Nullable.GetUnderlyingType(targetType) ?? targetType);
                }

                return JsonConvert.DeserializeObject(serializedValue, targetType);
            }
            catch
            {
                return null;
            }
        }

        public async Task UpdateEmployeeStatusAsync(Guid employeeId, EmployeeCompanyStatus status)
        {
            await employeesRepository.UpdateEmployeeStatusAsync(employeeId, status);
        }
    }
}
