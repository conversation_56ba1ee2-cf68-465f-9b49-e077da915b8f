using Microsoft.EntityFrameworkCore;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;

namespace WorkTimeApi.Database.Repositories.Employees
{
    public class EmployeesRepository(IDbContextFactory<WorkTimeApiDbContext> dbContextFactory)
        : BaseRepository<Employee>(dbContextFactory), IEmployeesRepository
    {
        public async Task<Employee> AddEmployeeAsync(Employee employee)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();
            var existingEmployee = await context.Employees.FindAsync(employee.Id);
            if (existingEmployee is not null)
                return existingEmployee;

            await context.Employees.AddAsync(employee);
            await context.SaveChangesAsync();

            return employee;
        }

        public async Task<Employee> AddEmployeeToCompanyAsync(Employee employee, Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingEmployee = await context.Employees.FindAsync(employee.Id);
            if (existingEmployee is null)
            {
                await context.Employees.AddAsync(employee);
                existingEmployee = employee;
            }

            employee.CompanyId = companyId;
            employee.Status = EmployeeCompanyStatus.Active;

            await context.SaveChangesAsync();

            return employee;
        }

        public async Task<Employee?> GetEmployeeAsync(Guid id)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Employees.FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<Employee?> GetFullEmployeeAsync(Guid id)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Employees
                .Include(e => e.EmployeeAddresses)
                .ThenInclude(ea => ea.Address)
                .Include(e => e.EmployeeBankAccounts)
                .ThenInclude(eb => eb.BankAccount)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<Employee?> GetUserEmployeeAsync(Guid userId, Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Employees.FirstOrDefaultAsync(e => e.UserId == userId && e.CompanyId == companyId);
        }

        public async Task<BankAccount?> GetEmployeeIbanAsync(Guid employeeId, BankAccountPurpose purpose)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var bankAccounts = await context.EmployeeBankAccounts.Include(eb => eb.BankAccount)
                .FirstOrDefaultAsync(e => e.EmployeeId == employeeId && e.BankAccount.Purpose == purpose);

            return bankAccounts?.BankAccount;
        }

        public async Task<List<Role>> GetEmployeeRoles(Guid employeeId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.RoleEmployees.Where(e => e.EmployeeId == employeeId).Select(re => re.Role).ToListAsync();
        }

        public async Task<bool> CheckIfEmployeeAlreadyExists(Employee employee)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingEmployee = await context.Employees.FindAsync(employee.Id);
            return existingEmployee is not null;
        }

        public async Task UpdateEmployeeAsync(Employee employee)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingEmployee = await context.Employees.FindAsync(employee.Id);
            if (existingEmployee is not null)
            {
                context.Entry(existingEmployee).CurrentValues.SetValues(employee);
                await context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<Employee>> GetEmployeesAsync(Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var employees = await context.Employees.Include(e => e.Payrolls)
                .Where(e => e.CompanyId == companyId && e.Status == EmployeeCompanyStatus.Active)
                .ToListAsync();

            return employees;
        }

        public async Task<List<Address>> GetEmployeeAddressesAsync(Guid employeeId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.EmployeeAddress
                .Where(e => e.EmployeeId == employeeId)
                .Include(e => e.Address)
                    .ThenInclude(a => a.City)
                .Include(e => e.Address)
                    .ThenInclude(a => a.District)
                .Include(e => e.Address)
                    .ThenInclude(a => a.Municipality)
                .Select(ea => ea.Address)
                .ToListAsync();
        }

        public async Task<bool> EditEmployeeAsync(Employee employee)
        {
            try
            {
                using var context = await dbContextFactory.CreateDbContextAsync();

                var employeeAddress = await context.EmployeeAddress
                   .Include(ea => ea.Employee)
                   .Include(ea => ea.Address)
                   .Where(ea => ea.EmployeeId == employee.Id)
                   .FirstOrDefaultAsync();

                if (employeeAddress == null)
                {
                    return false;
                }

                if (employee != null)
                    context.Entry(employeeAddress.Employee).CurrentValues.SetValues(employee);

                if (employee?.EmployeeAddresses != null)
                    context.Entry(employeeAddress.Address).CurrentValues.SetValues(employee.EmployeeAddresses);

                await context.SaveChangesAsync();

                return true;
            }

            //TO DO Exeptions
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task ImportEmployeeCompaniesAsync(IEnumerable<Guid> employeeIds, Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingPairs = await context.Employees
                .Where(ec => ec.CompanyId == companyId && employeeIds.Contains(ec.Id))
                .ToListAsync();

            existingPairs.ForEach(e => e.Status = EmployeeCompanyStatus.Active);

            foreach (var payroll in existingPairs)
            {
                payroll.Status = EmployeeCompanyStatus.Active;
                context.Entry(payroll).State = EntityState.Modified;
            }

            await context.SaveChangesAsync();
        }

        public async Task AddPendingRoleEmployeeAsync(PendingRoleEmployee pendingRoleEmployee)
        {
            var context = await dbContextFactory.CreateDbContextAsync();
            await context.PendingRoleEmployees.AddAsync(pendingRoleEmployee);
            await context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Employee>> GetEmployeeCompaniesAsync(IEnumerable<Guid> companyIds)
        {
            var context = await dbContextFactory.CreateDbContextAsync();
            var employees = new List<Employee>();

            const int batchSize = 1000;
            for (int i = 0; i < companyIds.Count(); i += batchSize)
            {
                var batch = companyIds.Skip(i).Take(batchSize).ToList();
                var batchEmployees = await context.Employees
                    .Where(e => batch.Contains(e.CompanyId))
                    .ToListAsync();

                employees.AddRange(batchEmployees);
            }

            return employees;
        }

        public async Task<IEnumerable<Employee>> GetEmployeePayrollListAsync(Guid companyId)
        {
            await using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Employees
               .Where(ec => ec.CompanyId == companyId
               && ec.Status == EmployeeCompanyStatus.Active)
               .Include(ec => ec.Payrolls.Where(p => p.CompanyId == companyId))
               .ThenInclude(p => p.AnnexPayrolls)
               .ToListAsync();
        }

        public async Task<IEnumerable<Employee>> GetUserEmployeePayrollListAsync(Guid userId, Guid companyId)
        {
            await using var context = await dbContextFactory.CreateDbContextAsync();
            return await context.Employees
                .Include(ec => ec.Payrolls)
                    .ThenInclude(p => p.AnnexPayrolls)
                .Where(ec => ec.UserId == userId
                    && ec.CompanyId == companyId
                    && ec.Status == EmployeeCompanyStatus.Active).ToListAsync();
        }

        public async Task<IEnumerable<Employee>> GetEmployeeAsync(Guid companyId, string permission)
        {
            var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Employees.Where(e => e.CompanyId == companyId)
                .Include(u => u.EmployeeRoles)
                .ThenInclude(ur => ur.Role)
                .ThenInclude(r => r.PermissionRoles)
                .ThenInclude(pr => pr.Permission)
                .Where(rt => rt.EmployeeRoles
                                .Select(ur => ur.Role)
                                .Any(pr => pr != null && pr.PermissionRoles.Select(pr => pr.Permission).Any(p => p.Name == permission)))
                .ToListAsync();
        }

        public async Task<Employee> AddEmployeeToCompanyAsync(Guid companyId, EmployeeCompanyStatus status, Guid userId)
        {
            var context = await dbContextFactory.CreateDbContextAsync();

            var existingEmployee = await context.Employees.FirstOrDefaultAsync(e => e.UserId == userId && e.CompanyId == companyId);
            if (existingEmployee == null)
            {
                var user = await context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                var newEmployee = new Employee
                {
                    UserId = userId,
                    FirstName = user.FirstName,
                    SecondName = user.SecondName,
                    LastName = user.LastName,
                    Email = user.Email,
                    CompanyId = companyId,
                    Status = status
                };

                await context.Employees.AddAsync(newEmployee);
                await context.SaveChangesAsync();
                return newEmployee;
            }

            existingEmployee.CompanyId = companyId;
            existingEmployee.Status = status;
            await context.SaveChangesAsync();

            return existingEmployee;
        }

        public async Task<Employee?> GetUserEmployeePermissionAsync(Guid userId, Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var employee = await context.Employees
                .Include(e => e.EmployeeRoles).ThenInclude(er => er.Role)
                .Include(e => e.Payrolls)
                .ThenInclude(a => a.AnnexPayrolls)
                .FirstOrDefaultAsync(e => e.UserId == userId && e.CompanyId == companyId);

            return employee;
        }

        public async Task<Employee> DoesEmployeeExistInCompanyAsync(string email, string egn, Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var employee = await context.Employees
                .Include(e => e.User)
                .FirstOrDefaultAsync(e => e.CompanyId == companyId
                    && ((!string.IsNullOrEmpty(email) && e.User.Email.ToLower() == email.ToLower())
                        || (!string.IsNullOrEmpty(egn) && e.EGN == egn)));

            return employee;
        }

        public async Task<Employee> UpdateUserEmployeeStatusAsync(Guid userId, Guid companyId, EmployeeCompanyStatus status)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var employee = await context.Employees.FirstOrDefaultAsync(e => e.UserId == userId && e.CompanyId == companyId);

            employee.Status = status;
            await context.SaveChangesAsync();

            return employee;
        }

        public async Task<Employee> UpdateEmployeeStatusAsync(Guid employeeId, EmployeeCompanyStatus status)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var employee = await context.Employees.FirstOrDefaultAsync(e => e.Id == employeeId);

            employee.Status = status;
            await context.SaveChangesAsync();

            return employee;
        }

        public async Task SaveEmployeePropertyEditsAsync(IEnumerable<EmployeePropertyEdit> propertyEdits)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            context.EmployeePropertyEdits.AddRange(propertyEdits);
            await context.SaveChangesAsync();
        }

        public async Task<List<EmployeePropertyEdit>> GetEmployeePropertyEditsAsync(Guid employeeId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.EmployeePropertyEdits
                .Where(epe => epe.EmployeeId == employeeId)
                .OrderByDescending(epe => epe.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<EmployeePropertyEdit>> GetPendingEmployeePropertyEditsAsync(Guid employeeId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.EmployeePropertyEdits
                .Where(epe => epe.EmployeeId == employeeId && epe.EditStatus == EditStatus.Pending)
                .OrderByDescending(epe => epe.CreatedAt)
                .ToListAsync();
        }

        public async Task<EmployeePropertyEdit?> GetEmployeePropertyEditByIdAsync(Guid propertyEditId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.EmployeePropertyEdits.FindAsync(propertyEditId);
        }

        public async Task UpdateEmployeePropertyEditStatusAsync(Guid propertyEditId, EditStatus newStatus)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var propertyEdit = await context.EmployeePropertyEdits.FindAsync(propertyEditId);
            if (propertyEdit != null)
            {
                propertyEdit.EditStatus = newStatus;
                propertyEdit.UpdatedAt = DateTime.UtcNow;
                await context.SaveChangesAsync();
            }
        }

        public async Task UpdateAllEmployeePropertyEditStatusAsync(Guid employeeId, EditStatus newStatus)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var propertyEdits = await context.EmployeePropertyEdits
                .Where(epe => epe.EmployeeId == employeeId && epe.EditStatus == EditStatus.Pending)
                .ToListAsync();

            foreach (var edit in propertyEdits)
            {
                edit.EditStatus = newStatus;
                edit.UpdatedAt = DateTime.UtcNow;
            }

            await context.SaveChangesAsync();
        }

        public async Task DeleteEmployeePropertyEditsAsync(Guid employeeId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var propertyEdits = context.EmployeePropertyEdits
                .Where(epe => epe.EmployeeId == employeeId);

            context.EmployeePropertyEdits.RemoveRange(propertyEdits);
            await context.SaveChangesAsync();
        }

        public async Task DeleteEmployeePropertyEditAsync(Guid propertyEditId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var propertyEdit = await context.EmployeePropertyEdits.FindAsync(propertyEditId);
            if (propertyEdit != null)
            {
                context.EmployeePropertyEdits.Remove(propertyEdit);
                await context.SaveChangesAsync();
            }
        }

        public async Task UpsertEmployeePropertyEditsAsync(Guid employeeId, IEnumerable<EmployeePropertyEdit> propertyEdits)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingPropertyEdits = await context.EmployeePropertyEdits
                .Where(epe => epe.EmployeeId == employeeId)
                .ToListAsync();

            foreach (var newPropertyEdit in propertyEdits)
            {
                var existingEdit = existingPropertyEdits.FirstOrDefault(epe =>
                    epe.ObjectName == newPropertyEdit.ObjectName &&
                    epe.ObjectId == newPropertyEdit.ObjectId &&
                    epe.PropertyName == newPropertyEdit.PropertyName);

                if (existingEdit != null)
                {
                    existingEdit.EditorId = newPropertyEdit.EditorId;
                    existingEdit.EditSource = newPropertyEdit.EditSource;
                    existingEdit.EditStatus = newPropertyEdit.EditStatus;
                    existingEdit.NewValue = newPropertyEdit.NewValue;
                    existingEdit.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    context.EmployeePropertyEdits.Add(newPropertyEdit);
                }
            }

            await context.SaveChangesAsync();
        }
    }
}
